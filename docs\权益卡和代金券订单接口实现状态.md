# 权益卡和代金券订单接口实现状态

## 概述

本文档记录了权益卡订单和代金券订单相关接口的实现状态，帮助开发团队了解哪些功能已经完成，哪些还需要开发。

## 图例说明

- ✅ **已实现**: 接口已经完全实现并可以使用
- ⚠️ **部分实现**: 接口基本功能已实现，但可能缺少管理员权限或特殊场景处理
- ❌ **未实现**: 接口尚未实现，需要开发
- 🔄 **需要验证**: 接口可能已实现但需要验证功能完整性

## 权益卡订单接口实现状态

### 用户端接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 查询权益卡订单列表 | `GET /membership-card-orders` | ✅ | 已实现，支持用户ID筛选 |
| 查询权益卡订单详情 | `GET /membership-card-orders/{id}` | ✅ | 已实现 |
| 创建权益卡订单 | `POST /membership-card-orders` | ✅ | 已实现 |
| 支付权益卡订单 | `POST /membership-card-orders/{sn}/pay` | ✅ | 已实现 |
| 取消权益卡订单 | `PUT /membership-card-orders/{sn}/cancel` | ✅ | 已实现 |
| 通过客户接口创建订单 | `POST /customers/{customerId}/membership-card-order` | ✅ | 已实现 |
| 查询可用权益卡 | `GET /customers/{customerId}/available-membership-cards` | ✅ | 已实现 |

### 管理端接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 查询所有权益卡订单 | `GET /membership-card-orders/all` | ✅ | 已实现，管理端可用 |
| 管理端退款权益卡订单 | `POST /admin/membership-card-orders/{id}/refund` | ❌ | **未实现，需要创建专门控制器** |
| 管理端删除权益卡订单 | `DELETE /admin/membership-card-orders/{id}` | ❌ | **未实现，需要创建专门控制器** |

### 微信支付接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 发起微信支付 | `POST /wepay/membership-card-order/{sn}` | ✅ | 已实现 |
| 支付回调 | `POST /wepay/membership-card-order/notify` | ✅ | 已实现 |
| 管理端查询支付状态 | `GET /admin/wepay/transactions/sn/{sn}` | ✅ | 已实现，通用接口 |

### 用户权益卡管理接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 查询用户权益卡列表 | `GET /customer-membership-cards` | ✅ | 已实现 |
| 查询用户权益卡详情 | `GET /customer-membership-cards/{id}` | ✅ | 已实现 |
| 手动发放权益卡 | `POST /customer-membership-cards` | ✅ | 已实现，支持管理员发放 |
| 更新用户权益卡 | `PUT /customer-membership-cards/{id}` | ✅ | 已实现 |
| 禁用用户权益卡 | `POST /customer-membership-cards/{id}/disable` | ✅ | 已实现 |
| 撤销用户权益卡 | `POST /customer-membership-cards/{id}/revoke` | ✅ | 已实现 |

## 代金券订单接口实现状态

### 用户端接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 查询代金券订单列表 | `GET /coupon-orders` | ✅ | 已实现，支持用户ID筛选 |
| 查询代金券订单详情 | `GET /coupon-orders/{id}` | ✅ | 已实现 |
| 创建代金券订单 | `POST /coupon-orders` | ✅ | 已实现 |
| 支付代金券订单 | `POST /coupon-orders/{sn}/pay` | ✅ | 已实现 |
| 取消代金券订单 | `PUT /coupon-orders/{sn}/cancel` | 🔄 | 需要验证是否已实现 |
| 通过客户接口创建订单 | `POST /customers/{customerId}/coupon-order` | ✅ | 已实现 |
| 查询可用代金券 | `GET /customers/{customerId}/available-coupons` | ✅ | 已实现 |

### 管理端接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 查询所有代金券订单 | `GET /coupon-orders/all` | 🔄 | 需要验证是否已实现 |
| 管理端退款代金券订单 | `POST /admin/coupon-orders/{id}/refund` | ❌ | **未实现，需要创建专门控制器** |
| 管理端删除代金券订单 | `DELETE /admin/coupon-orders/{id}` | ❌ | **未实现，需要创建专门控制器** |

### 微信支付接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 发起微信支付 | `POST /wepay/coupon-order/{sn}` | 🔄 | 需要验证是否已实现 |
| 支付回调 | `POST /wepay/coupon-order/notify` | 🔄 | 需要验证是否已实现 |

### 用户代金券管理接口
| 接口 | 路径 | 状态 | 说明 |
|------|------|------|------|
| 查询用户代金券列表 | `GET /customer-coupons` | ✅ | 已实现 |
| 查询用户代金券详情 | `GET /customer-coupons/{id}` | ✅ | 已实现 |
| 手动发放代金券 | `POST /customer-coupons` | ✅ | 已实现，支持管理员发放 |
| 更新用户代金券 | `PUT /customer-coupons/{id}` | ✅ | 已实现 |
| 删除用户代金券 | `DELETE /customer-coupons/{id}` | ✅ | 已实现 |

## 需要优先实现的接口

### 高优先级（核心管理功能）
1. **管理端权益卡订单退款**: `POST /admin/membership-card-orders/{id}/refund`
2. **管理端权益卡订单删除**: `DELETE /admin/membership-card-orders/{id}`
3. **管理端代金券订单退款**: `POST /admin/coupon-orders/{id}/refund`
4. **管理端代金券订单删除**: `DELETE /admin/coupon-orders/{id}`

### 中优先级（完善功能）
1. **代金券订单取消**: `PUT /coupon-orders/{sn}/cancel`
2. **代金券微信支付**: `POST /wepay/coupon-order/{sn}`
3. **代金券支付回调**: `POST /wepay/coupon-order/notify`

## 实现建议

### 创建管理端控制器
建议创建以下两个专门的管理端控制器：

1. **`src/controller/admin/membership-card-order-admin.controller.ts`**
   - 实现权益卡订单的管理端退款和删除功能
   - 参考 `src/controller/admin/additional-service-admin.controller.ts` 的实现方式

2. **`src/controller/admin/coupon-order-admin.controller.ts`**
   - 实现代金券订单的管理端退款和删除功能
   - 参考 `src/controller/admin/additional-service-admin.controller.ts` 的实现方式

### 服务层方法
需要在相应的服务类中添加管理员专用方法：

1. **`MembershipCardOrderService`**
   - `adminRefundMembershipCardOrder(id, operatorId, reason)`
   - `adminDeleteMembershipCardOrder(id, operatorId, reason)`

2. **`CouponOrderService`**
   - `adminRefundCouponOrder(id, operatorId, reason)`
   - `adminDeleteCouponOrder(id, operatorId, reason)`

### 微信支付支持
需要在 `WepayService` 中添加：
- `adminRefundMembershipCardOrder(sn, refund?)`
- `adminRefundCouponOrder(sn, refund?)`

## 测试建议

1. **功能测试**: 确保所有已实现的接口功能正常
2. **权限测试**: 验证管理员权限控制是否正确
3. **业务逻辑测试**: 验证退款、删除等操作的业务逻辑
4. **数据一致性测试**: 确保操作后数据状态一致

## 更新日志

- 2025-07-21: 初始状态梳理，标识未实现的管理端接口
