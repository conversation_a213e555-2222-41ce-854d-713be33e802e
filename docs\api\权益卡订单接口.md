# 权益卡订单相关API接口

## 权益卡订单状态枚举
```typescript
enum MembershipCardOrderStatus {
  待付款 = 'pending_payment',
  已付款 = 'paid',
  已取消 = 'cancelled',
  已退款 = 'refunded'
}
```

## 1. 权益卡订单查询接口

### 1.1 查询所有权益卡订单列表（管理端）
- **接口**: `GET /membership-card-orders/all`
- **描述**: 管理端查询所有权益卡订单列表，支持分页和状态筛选
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `status` (string): 订单状态筛选，多个状态用逗号分隔
- **返回**: 包含订单列表、客户信息、权益卡类型信息

### 1.2 查询用户权益卡订单列表（用户端）
- **接口**: `GET /membership-card-orders`
- **描述**: 查询指定用户的权益卡订单列表
- **参数**:
  - `customerId` (number): 用户ID（必填）
  - `status` (string): 订单状态筛选，多个状态用逗号分隔
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10

### 1.3 查询权益卡订单详情
- **接口**: `GET /membership-card-orders/{id}`
- **描述**: 查询指定权益卡订单的详细信息
- **参数**: `id` (number): 订单ID
- **返回**: 包含订单详情、客户信息、权益卡类型信息

### 1.4 通过客户接口创建权益卡订单
- **接口**: `POST /customers/{customerId}/membership-card-order`
- **描述**: 为指定客户创建权益卡订单
- **参数**: `customerId` (number): 客户ID
- **请求体**:
  ```typescript
  {
    cardTypeId: number;    // 权益卡类型ID（必填）
    remark?: string;       // 备注（可选）
  }
  ```

### 1.5 查询用户可用权益卡
- **接口**: `GET /customers/{customerId}/available-membership-cards`
- **描述**: 查询用户可用的权益卡列表，支持按服务和金额筛选
- **参数**:
  - `customerId` (number): 客户ID
  - `serviceId` (number): 服务ID（可选，用于筛选适用的权益卡）
  - `amount` (number): 订单金额（可选，用于筛选满足条件的权益卡）

## 2. 权益卡订单操作接口

### 2.1 创建权益卡订单
- **接口**: `POST /membership-card-orders`
- **描述**: 创建权益卡订单
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
    cardTypeId: number;    // 权益卡类型ID（必填）
    remark?: string;       // 备注（可选）
  }
  ```
- **返回**: 创建的订单信息

### 2.2 支付权益卡订单
- **接口**: `POST /membership-card-orders/{sn}/pay`
- **描述**: 支付权益卡订单
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **业务逻辑**:
  1. 验证订单状态为"待付款"
  2. 验证订单归属
  3. 更新订单状态为"已付款"
  4. 创建用户权益卡记录
  5. 更新用户会员状态为权益会员

### 2.3 取消权益卡订单
- **接口**: `POST /membership-card-orders/{sn}/cancel`
- **描述**: 取消权益卡订单
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **限制**: 只有"待付款"状态的订单可以取消

## 3. 微信支付相关接口

### 3.1 发起权益卡订单微信支付
- **接口**: `POST /wepay/membership-card-order/{sn}`
- **描述**: 发起权益卡订单的微信支付
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **返回**: 微信支付预支付信息

### 3.2 权益卡订单支付回调
- **接口**: `POST /wepay/membership-card-order/notify`
- **描述**: 微信支付回调接口，处理权益卡订单支付结果
- **说明**: 微信支付成功后自动调用，更新订单状态并创建权益卡

## 4. 管理端权益卡订单管理

### 4.1 管理端查询微信支付状态
- **接口**: `GET /admin/wepay/transactions/sn/{sn}`
- **描述**: 管理端查询权益卡订单的微信支付状态并同步本地状态
- **参数**: `sn` (string): 订单编号
- **功能**: 查询微信支付状态并同步到本地数据库

### 4.2 管理端权益卡订单退款
- **接口**: `POST /admin/membership-card-orders/{id}/refund`
- **描述**: 管理端处理权益卡订单退款（任何状态）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 退款原因（可选）
  }
  ```

### 4.3 管理端删除权益卡订单
- **接口**: `DELETE /admin/membership-card-orders/{id}`
- **描述**: 管理端删除权益卡订单（任何状态）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 删除原因（可选）
  }
  ```

## 5. 开放接口

### 5.1 查询权益卡类型列表
- **接口**: `GET /openapi/rights-card`
- **描述**: 查询启用的权益卡类型列表（无需认证）
- **返回**: 启用状态的权益卡类型列表

## 数据结构说明

### 权益卡订单实体
```typescript
interface MembershipCardOrderAttributes {
  id: number;                           // 订单ID
  sn: string;                          // 订单编号（格式：MC + 时间戳 + 4位随机数）
  customerId: number;                  // 关联的用户ID
  cardTypeId: number;                  // 关联的权益卡类型ID
  amount: number;                      // 订单金额
  status: MembershipCardOrderStatus;   // 订单状态
  createdAt?: Date;                    // 创建时间
  payTime?: Date;                      // 支付时间
  cancelTime?: Date;                   // 取消时间
  refundTime?: Date;                   // 退款时间
  prepayId?: string;                   // 微信支付预支付ID
  remark?: string;                     // 备注
  customer?: Customer;                 // 关联的用户
  cardType?: MembershipCardType;       // 关联的权益卡类型
}
```

### 权益卡创建规则
1. **权益卡类型验证**: 验证权益卡类型存在且启用
2. **订单编号生成**: MC + 当前时间戳 + 4位随机数
3. **金额设置**: 使用权益卡类型的价格
4. **有效期计算**: 根据权益卡类型的有效天数计算到期时间
5. **次数设置**: 根据权益卡类型的使用限制设置剩余次数
6. **会员状态更新**: 支付成功后更新用户为权益会员
