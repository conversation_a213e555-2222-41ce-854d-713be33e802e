# 代金券订单相关API接口

## 代金券订单状态枚举
```typescript
enum CouponOrderStatus {
  待付款 = 'pending_payment',
  已付款 = 'paid',
  已取消 = 'cancelled',
  已退款 = 'refunded'
}
```

## 1. 代金券订单查询接口

### 1.1 查询用户代金券订单列表（用户端）
- **接口**: `GET /coupon-orders`
- **描述**: 查询指定用户的代金券订单列表
- **参数**:
  - `customerId` (number): 用户ID（必填）
  - `status` (string): 订单状态筛选，多个状态用逗号分隔
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10

### 1.2 查询代金券订单详情
- **接口**: `GET /coupon-orders/{id}`
- **描述**: 查询指定代金券订单的详细信息
- **参数**: `id` (number): 订单ID
- **返回**: 包含订单详情、客户信息、代金券信息

### 1.3 通过客户接口创建代金券订单
- **接口**: `POST /customers/{customerId}/coupon-order`
- **描述**: 为指定客户创建代金券订单
- **参数**: `customerId` (number): 客户ID
- **请求体**:
  ```typescript
  {
    couponId: number;      // 代金券ID（必填）
    remark?: string;       // 备注（可选）
  }
  ```

### 1.4 查询用户可用代金券
- **接口**: `GET /customers/{customerId}/available-coupons`
- **描述**: 查询用户可用的代金券列表，支持按服务和金额筛选
- **参数**:
  - `customerId` (number): 客户ID
  - `serviceId` (number): 服务ID（可选，用于筛选适用的代金券）
  - `amount` (number): 订单金额（可选，用于筛选满足条件的代金券）

## 2. 代金券订单操作接口

### 2.1 创建代金券订单
- **接口**: `POST /coupon-orders`
- **描述**: 创建代金券订单
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
    couponId: number;      // 代金券ID（必填）
    remark?: string;       // 备注（可选）
  }
  ```
- **返回**: 创建的订单信息

### 2.2 支付代金券订单
- **接口**: `POST /coupon-orders/{sn}/pay`
- **描述**: 支付代金券订单
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **业务逻辑**:
  1. 验证订单状态为"待付款"
  2. 验证订单归属
  3. 更新订单状态为"已付款"
  4. 创建用户代金券记录

### 2.3 取消代金券订单
- **接口**: `POST /coupon-orders/{sn}/cancel`
- **描述**: 取消代金券订单
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **限制**: 只有"待付款"状态的订单可以取消

## 3. 微信支付相关接口

### 3.1 发起代金券订单微信支付
- **接口**: `POST /wepay/coupon-order/{sn}`
- **描述**: 发起代金券订单的微信支付
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    appid: string;    // 微信小程序appid（必填）
  }
  ```
- **返回**: 微信支付预支付信息

### 3.2 代金券订单支付回调
- **接口**: `POST /wepay/coupon-order/notify`
- **描述**: 微信支付回调接口，处理代金券订单支付结果
- **说明**: 微信支付成功后自动调用，更新订单状态并创建代金券

## 4. 管理端代金券订单管理

### 4.1 管理端查询微信支付状态
- **接口**: `GET /admin/wepay/transactions/sn/{sn}`
- **描述**: 管理端查询代金券订单的微信支付状态并同步本地状态
- **参数**: `sn` (string): 订单编号
- **功能**: 查询微信支付状态并同步到本地数据库

### 4.2 管理端代金券订单退款
- **接口**: `POST /admin/coupon-orders/{id}/refund`
- **描述**: 管理端处理代金券订单退款（任何状态）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 退款原因（可选）
  }
  ```

### 4.3 管理端删除代金券订单
- **接口**: `DELETE /admin/coupon-orders/{id}`
- **描述**: 管理端删除代金券订单（任何状态）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 删除原因（可选）
  }
  ```

## 5. 用户代金券管理接口

### 5.1 查询用户代金券列表
- **接口**: `GET /customer-coupons`
- **描述**: 查询用户代金券列表
- **参数**:
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - 其他查询条件参数

### 5.2 查询用户代金券详情
- **接口**: `GET /customer-coupons/{id}`
- **描述**: 查询指定用户代金券的详细信息
- **参数**: `id` (number): 用户代金券ID

### 5.3 新增用户代金券
- **接口**: `POST /customer-coupons`
- **描述**: 新增用户代金券（管理员发放）
- **请求体**: 包含代金券信息和操作员ID

### 5.4 更新用户代金券
- **接口**: `PUT /customer-coupons/{id}`
- **描述**: 更新用户代金券信息
- **参数**: `id` (number): 用户代金券ID

### 5.5 删除用户代金券
- **接口**: `DELETE /customer-coupons/{id}`
- **描述**: 删除用户代金券
- **参数**: `id` (number): 用户代金券ID

## 数据结构说明

### 代金券订单实体
```typescript
interface CouponOrderAttributes {
  id: number;                      // 订单ID
  sn: string;                     // 订单编号（格式：CP + 时间戳 + 4位随机数）
  customerId: number;             // 关联的用户ID
  couponId: number;               // 关联的代金券ID
  amount: number;                 // 订单金额
  status: CouponOrderStatus;      // 订单状态
  createdAt?: Date;               // 创建时间
  payTime?: Date;                 // 支付时间
  cancelTime?: Date;              // 取消时间
  refundTime?: Date;              // 退款时间
  prepayId?: string;              // 微信支付预支付ID
  remark?: string;                // 备注
  customer?: Customer;            // 关联的用户
  coupon?: Coupon;                // 关联的代金券
}
```

### 代金券创建规则
1. **代金券验证**: 验证代金券存在且启用
2. **订单编号生成**: CP + 当前时间戳 + 4位随机数
3. **金额设置**: 使用代金券的价格
4. **有效期计算**: 根据代金券的有效期设置
5. **使用条件**: 根据代金券的使用条件设置

## 代金券使用规则

### 在订单中使用代金券
1. **适用性检查**: 检查代金券是否适用于当前服务
2. **金额条件**: 检查订单金额是否满足代金券使用条件
3. **有效期检查**: 检查代金券是否在有效期内
4. **状态检查**: 检查代金券状态为可用
5. **使用记录**: 使用后创建使用记录并更新状态
