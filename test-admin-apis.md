# 管理端权益卡和代金券订单接口测试

## 测试环境
- 服务器地址: http://127.0.0.1:3001
- 测试时间: 2025-07-22

## 权益卡订单管理端接口测试

### 1. 查询所有权益卡订单
```bash
curl -X GET "http://127.0.0.1:3001/admin/membership-card-orders/?current=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 2. 查询权益卡订单详情
```bash
curl -X GET "http://127.0.0.1:3001/admin/membership-card-orders/1" \
  -H "Content-Type: application/json"
```

### 3. 管理端退款权益卡订单
```bash
curl -X POST "http://127.0.0.1:3001/admin/membership-card-orders/1/refund" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "reason": "测试退款",
    "refundAmount": 100
  }'
```

### 4. 管理端删除权益卡订单
```bash
curl -X DELETE "http://127.0.0.1:3001/admin/membership-card-orders/1" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "reason": "测试删除"
  }'
```

### 5. 管理端取消权益卡订单
```bash
curl -X POST "http://127.0.0.1:3001/admin/membership-card-orders/1/cancel" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "reason": "测试取消"
  }'
```

### 6. 权益卡订单统计
```bash
curl -X GET "http://127.0.0.1:3001/admin/membership-card-orders/statistics" \
  -H "Content-Type: application/json"
```

## 代金券订单管理端接口测试

### 1. 查询所有代金券订单
```bash
curl -X GET "http://127.0.0.1:3001/admin/coupon-orders/?current=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 2. 查询代金券订单详情
```bash
curl -X GET "http://127.0.0.1:3001/admin/coupon-orders/1" \
  -H "Content-Type: application/json"
```

### 3. 管理端退款代金券订单
```bash
curl -X POST "http://127.0.0.1:3001/admin/coupon-orders/1/refund" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "reason": "测试退款",
    "refundAmount": 50
  }'
```

### 4. 管理端删除代金券订单
```bash
curl -X DELETE "http://127.0.0.1:3001/admin/coupon-orders/1" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "reason": "测试删除"
  }'
```

### 5. 管理端取消代金券订单
```bash
curl -X POST "http://127.0.0.1:3001/admin/coupon-orders/1/cancel" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "reason": "测试取消"
  }'
```

### 6. 代金券订单统计
```bash
curl -X GET "http://127.0.0.1:3001/admin/coupon-orders/statistics" \
  -H "Content-Type: application/json"
```

## 微信支付接口测试

### 1. 权益卡订单微信支付
```bash
curl -X POST "http://127.0.0.1:3001/wepay/membership-card-order/MC1234567890" \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "wx1234567890"
  }'
```

### 2. 代金券订单微信支付
```bash
curl -X POST "http://127.0.0.1:3001/wepay/coupon-order/CP1234567890" \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "wx1234567890"
  }'
```

### 3. 管理端查询微信支付状态
```bash
curl -X GET "http://127.0.0.1:3001/admin/wepay/transactions/sn/MC1234567890" \
  -H "Content-Type: application/json"
```

## 预期结果

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    // 具体数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 1,
  "msg": "错误信息",
  "data": null
}
```

## 测试注意事项

1. **数据准备**: 测试前需要确保数据库中有相应的测试数据
2. **权限验证**: 管理端接口可能需要管理员权限验证
3. **微信支付**: 微信支付接口需要有效的订单和appid
4. **事务回滚**: 测试退款和删除操作时注意数据一致性
5. **日志查看**: 可以通过服务器日志查看详细的执行过程

## 功能验证清单

- [ ] 权益卡订单查询功能
- [ ] 权益卡订单退款功能
- [ ] 权益卡订单删除功能
- [ ] 权益卡订单取消功能
- [ ] 权益卡订单统计功能
- [ ] 代金券订单查询功能
- [ ] 代金券订单退款功能
- [ ] 代金券订单删除功能
- [ ] 代金券订单取消功能
- [ ] 代金券订单统计功能
- [ ] 权益卡微信支付功能
- [ ] 代金券微信支付功能
- [ ] 微信支付状态查询功能

## 测试结果记录

请在测试完成后记录测试结果：

### 权益卡订单接口测试结果
- 查询接口: ✅/❌
- 退款接口: ✅/❌
- 删除接口: ✅/❌
- 取消接口: ✅/❌
- 统计接口: ✅/❌

### 代金券订单接口测试结果
- 查询接口: ✅/❌
- 退款接口: ✅/❌
- 删除接口: ✅/❌
- 取消接口: ✅/❌
- 统计接口: ✅/❌

### 微信支付接口测试结果
- 权益卡支付: ✅/❌
- 代金券支付: ✅/❌
- 支付状态查询: ✅/❌
