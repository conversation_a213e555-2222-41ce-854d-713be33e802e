# 管理端代金券订单相关API接口

## 代金券订单状态枚举
```typescript
enum CouponOrderStatus {
  待付款 = 'pending_payment',
  已付款 = 'paid',
  已取消 = 'cancelled',
  已退款 = 'refunded'
}
```

## 1. 代金券订单查询接口

### 1.1 管理端查询所有代金券订单列表
- **接口**: `GET /coupon-orders/all`
- **描述**: 管理端查询所有代金券订单列表，支持分页和状态筛选
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `status` (string): 订单状态筛选，多个状态用逗号分隔
- **返回**: 包含订单列表、客户信息、代金券信息

### 1.2 管理端查询代金券订单详情
- **接口**: `GET /coupon-orders/{id}`
- **描述**: 管理端查询指定代金券订单的详细信息
- **参数**: `id` (number): 订单ID
- **返回**: 包含订单详情、客户信息、代金券信息

### 1.3 管理端查询用户代金券列表
- **接口**: `GET /customer-coupons`
- **描述**: 管理端查询用户代金券列表，支持分页和筛选
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `customerId` (number): 用户ID筛选
  - `status` (string): 代金券状态筛选
  - `couponId` (number): 代金券ID筛选
- **返回**: 包含用户代金券列表、客户信息、代金券信息

### 1.4 管理端查询用户代金券详情
- **接口**: `GET /customer-coupons/{id}`
- **描述**: 管理端查询指定用户代金券的详细信息
- **参数**: `id` (number): 用户代金券ID
- **返回**: 包含用户代金券详情、客户信息、代金券信息

## 2. 代金券订单管理接口

### 2.1 管理端创建代金券订单
- **接口**: `POST /coupon-orders`
- **描述**: 管理端创建代金券订单
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
    couponId: number;      // 代金券ID（必填）
    remark?: string;       // 备注（可选）
  }
  ```
- **返回**: 创建的订单信息

### 2.2 管理端支付代金券订单
- **接口**: `POST /coupon-orders/{sn}/pay`
- **描述**: 管理端支付代金券订单
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **业务逻辑**:
  1. 验证订单状态为"待付款"
  2. 验证订单归属
  3. 更新订单状态为"已付款"
  4. 创建用户代金券记录

### 2.3 管理端取消代金券订单
- **接口**: `PUT /coupon-orders/{sn}/cancel`
- **描述**: 管理端取消代金券订单
- **参数**: `sn` (string): 订单编号
- **请求体**:
  ```typescript
  {
    customerId: number;    // 用户ID（必填）
  }
  ```
- **限制**: 只有"待付款"状态的订单可以取消

### 2.4 管理端退款代金券订单
- **接口**: `POST /admin/coupon-orders/{id}/refund`
- **描述**: 管理端处理代金券订单退款（任何状态）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 退款原因（可选）
  }
  ```
- **业务逻辑**:
  1. 验证操作员权限
  2. 执行退款操作
  3. 更新订单状态为"已退款"
  4. 禁用相关的用户代金券

### 2.5 管理端删除代金券订单
- **接口**: `DELETE /admin/coupon-orders/{id}`
- **描述**: 管理端删除代金券订单（任何状态）
- **参数**: `id` (number): 订单ID
- **请求体**:
  ```typescript
  {
    operatorId: number;    // 操作员ID（必填）
    reason?: string;       // 删除原因（可选）
  }
  ```
- **业务逻辑**:
  1. 验证操作员权限
  2. 检查订单支付状态
  3. 如果已支付，执行退款操作
  4. 禁用相关的用户代金券
  5. 删除订单记录

## 3. 用户代金券管理接口

### 3.1 管理端手动发放代金券
- **接口**: `POST /customer-coupons`
- **描述**: 管理端手动为用户发放代金券
- **请求体**:
  ```typescript
  {
    customerId: number;       // 用户ID（必填）
    couponId: number;         // 代金券ID（必填）
    obtainTime?: Date;        // 获得时间（可选，默认当前时间）
    expiryTime?: Date;        // 过期时间（可选）
    status: string;           // 状态（必填，默认'unused'）
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **返回**: 创建的用户代金券信息

### 3.2 管理端更新用户代金券
- **接口**: `PUT /customer-coupons/{id}`
- **描述**: 管理端更新用户代金券信息
- **参数**: `id` (number): 用户代金券ID
- **请求体**:
  ```typescript
  {
    expiryTime?: Date;        // 过期时间（可选）
    status?: string;          // 状态（可选）
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **返回**: 更新结果

### 3.3 管理端禁用用户代金券
- **接口**: `POST /customer-coupons/{id}/disable`
- **描述**: 管理端禁用用户代金券
- **参数**: `id` (number): 用户代金券ID
- **请求体**:
  ```typescript
  {
    operatorId: number;       // 操作员ID（必填）
    description?: string;     // 描述（可选）
  }
  ```
- **业务逻辑**:
  1. 更新代金券状态为'expired'
  2. 记录变更日志

### 3.4 管理端删除用户代金券
- **接口**: `DELETE /customer-coupons/{id}`
- **描述**: 管理端删除用户代金券
- **参数**: `id` (number): 用户代金券ID
- **请求体**:
  ```typescript
  {
    operatorId: number;       // 操作员ID（必填）
    reason?: string;          // 删除原因（可选）
  }
  ```
- **业务逻辑**:
  1. 验证操作员权限
  2. 检查代金券使用状态
  3. 删除代金券记录

## 4. 微信支付管理接口

### 4.1 管理端查询微信支付状态
- **接口**: `GET /admin/wepay/transactions/sn/{sn}`
- **描述**: 管理端查询代金券订单的微信支付状态并同步本地状态
- **参数**: `sn` (string): 订单编号
- **功能**: 查询微信支付状态并同步到本地数据库
- **返回**:
  ```typescript
  {
    // 微信支付返回的原始数据
    ...wechatResult,
    // 本地同步结果
    localSyncResult: {
      synced: boolean;        // 是否同步成功
      message: string;        // 同步结果消息
    }
  }
  ```

## 5. 代金券管理接口

### 5.1 管理端查询代金券列表
- **接口**: `GET /admin/coupons`
- **描述**: 管理端查询代金券列表
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `isEnabled` (boolean): 是否启用筛选
  - `name` (string): 名称模糊查询
- **返回**: 代金券列表

### 5.2 管理端创建代金券
- **接口**: `POST /admin/coupons`
- **描述**: 管理端创建代金券
- **请求体**:
  ```typescript
  {
    name: string;             // 名称（必填）
    description?: string;     // 描述（可选）
    amount: number;           // 面额（必填）
    minAmount?: number;       // 最低消费金额（可选）
    price: number;            // 销售价格（必填）
    validFrom?: Date;         // 有效期开始时间（可选）
    validTo?: Date;           // 有效期结束时间（可选）
    isEnabled: boolean;       // 是否启用（必填）
    operatorId: number;       // 操作员ID（必填）
  }
  ```
- **返回**: 创建的代金券信息

### 5.3 管理端更新代金券
- **接口**: `PUT /admin/coupons/{id}`
- **描述**: 管理端更新代金券
- **参数**: `id` (number): 代金券ID
- **请求体**:
  ```typescript
  {
    name?: string;            // 名称（可选）
    description?: string;     // 描述（可选）
    amount?: number;          // 面额（可选）
    minAmount?: number;       // 最低消费金额（可选）
    price?: number;           // 销售价格（可选）
    validFrom?: Date;         // 有效期开始时间（可选）
    validTo?: Date;           // 有效期结束时间（可选）
    isEnabled?: boolean;      // 是否启用（可选）
    operatorId: number;       // 操作员ID（必填）
  }
  ```
- **返回**: 更新结果

### 5.4 管理端删除代金券
- **接口**: `DELETE /admin/coupons/{id}`
- **描述**: 管理端删除代金券
- **参数**: `id` (number): 代金券ID
- **请求体**:
  ```typescript
  {
    operatorId: number;       // 操作员ID（必填）
    reason?: string;          // 删除原因（可选）
  }
  ```
- **业务逻辑**:
  1. 验证操作员权限
  2. 检查是否有用户持有该代金券
  3. 删除代金券记录

## 6. 代金券使用记录查询

### 6.1 管理端查询代金券使用记录
- **接口**: `GET /admin/coupon-usage-records`
- **描述**: 管理端查询代金券使用记录
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `customerId` (number): 用户ID筛选
  - `couponId` (number): 代金券ID筛选
  - `orderId` (number): 订单ID筛选
  - `startTime` (string): 开始时间筛选
  - `endTime` (string): 结束时间筛选
- **返回**: 代金券使用记录列表

### 6.2 管理端查询代金券使用统计
- **接口**: `GET /admin/coupon-usage-statistics`
- **描述**: 管理端查询代金券使用统计
- **参数**:
  - `startTime` (string): 开始时间
  - `endTime` (string): 结束时间
  - `couponId` (number): 代金券ID筛选
- **返回**: 代金券使用统计数据

## 数据结构说明

### 代金券订单实体
```typescript
interface CouponOrderAttributes {
  id: number;                      // 订单ID
  sn: string;                     // 订单编号（格式：CP + 时间戳 + 4位随机数）
  customerId: number;             // 关联的用户ID
  couponId: number;               // 关联的代金券ID
  amount: number;                 // 订单金额
  status: CouponOrderStatus;      // 订单状态
  createdAt?: Date;               // 创建时间
  payTime?: Date;                 // 支付时间
  cancelTime?: Date;              // 取消时间
  refundTime?: Date;              // 退款时间
  prepayId?: string;              // 微信支付预支付ID
  remark?: string;                // 备注
  customer?: Customer;            // 关联的用户
  coupon?: Coupon;                // 关联的代金券
}
```

### 用户代金券实体
```typescript
interface CustomerCouponAttributes {
  id: number;                     // 用户代金券ID
  customerId: number;             // 用户ID
  couponId: number;               // 代金券ID
  obtainTime: Date;               // 获得时间
  expiryTime?: Date;              // 过期时间
  useTime?: Date;                 // 使用时间
  orderId?: number;               // 使用的订单ID
  status: string;                 // 状态（unused/used/expired）
  customer?: Customer;            // 关联的用户
  coupon?: Coupon;                // 关联的代金券
}
```
